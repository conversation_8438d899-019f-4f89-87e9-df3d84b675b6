import { collection, doc, getDocs, addDoc, updateDoc, deleteDoc, query, where, getDoc, setDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import { db, auth } from '../firebase';
import { Employee, AttendanceRecord, LeaveRequest, PendingApproval, User, UserRole } from '../App';

// Authentication services
export const loginWithEmailAndPassword = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
    
    if (userDoc.exists()) {
      return { ...userDoc.data(), id: userCredential.user.uid } as User;
    } else {
      throw new Error('User data not found');
    }
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

export const loginWithGoogle = async () => {
  try {
    const provider = new GoogleAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);
    
    // Check if user exists in Firestore
    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
    
    if (userDoc.exists()) {
      return { ...userDoc.data(), id: userCredential.user.uid } as User;
    } else {
      // Create a new user document if it doesn't exist
      const newUser: Omit<User, 'id'> = {
        name: userCredential.user.displayName || 'User',
        email: userCredential.user.email || '',
        role: 'employee', // Default role
        avatar: userCredential.user.photoURL || '',
      };
      
      await setDoc(doc(db, 'users', userCredential.user.uid), newUser);
      return { ...newUser, id: userCredential.user.uid } as User;
    }
  } catch (error) {
    console.error('Error logging in with Google:', error);
    throw error;
  }
};

export const logoutUser = async () => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error logging out:', error);
    throw error;
  }
};

// Employee services
export const getEmployees = async (): Promise<Employee[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, 'employees'));
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as Employee);
  } catch (error) {
    console.error('Error getting employees:', error);
    return [];
  }
};

export const getEmployeesByRole = async (role: UserRole): Promise<Employee[]> => {
  try {
    const q = query(collection(db, 'employees'), where('role', '==', role));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as Employee);
  } catch (error) {
    console.error(`Error getting ${role}s:`, error);
    return [];
  }
};

export const getSupervisors = async (): Promise<Employee[]> => {
  try {
    const q = query(collection(db, 'employees'), where('role', '==', 'supervisor'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as Employee);
  } catch (error) {
    console.error('Error getting supervisors:', error);
    return [];
  }
};

export const getEmployeesBySupervisor = async (supervisorId: string): Promise<Employee[]> => {
  try {
    const q = query(collection(db, 'employees'), where('supervisorId', '==', supervisorId));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as Employee);
  } catch (error) {
    console.error('Error getting employees by supervisor:', error);
    return [];
  }
};

export const addEmployee = async (employee: Omit<Employee, 'id'>): Promise<Employee> => {
  try {
    const docRef = await addDoc(collection(db, 'employees'), employee);
    return { ...employee, id: docRef.id };
  } catch (error) {
    console.error('Error adding employee:', error);
    throw error;
  }
};

export const updateEmployee = async (id: string, updates: Partial<Employee>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'employees', id), updates);
  } catch (error) {
    console.error('Error updating employee:', error);
    throw error;
  }
};

export const deleteEmployee = async (id: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'employees', id));
  } catch (error) {
    console.error('Error deleting employee:', error);
    throw error;
  }
};

// Attendance services
export const getAttendanceRecords = async (): Promise<AttendanceRecord[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, 'attendance'));
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as AttendanceRecord);
  } catch (error) {
    console.error('Error getting attendance records:', error);
    return [];
  }
};

export const addAttendanceRecord = async (record: Omit<AttendanceRecord, 'id'>): Promise<AttendanceRecord> => {
  try {
    const docRef = await addDoc(collection(db, 'attendance'), record);
    return { ...record, id: docRef.id };
  } catch (error) {
    console.error('Error adding attendance record:', error);
    throw error;
  }
};

// Leave request services
export const getLeaveRequests = async (): Promise<LeaveRequest[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, 'leaveRequests'));
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as LeaveRequest);
  } catch (error) {
    console.error('Error getting leave requests:', error);
    return [];
  }
};

export const submitLeaveRequest = async (request: Omit<LeaveRequest, 'id' | 'appliedDate' | 'status'>): Promise<LeaveRequest> => {
  try {
    const newRequest = {
      ...request,
      appliedDate: new Date().toISOString().split('T')[0],
      status: 'pending'
    };
    
    const docRef = await addDoc(collection(db, 'leaveRequests'), newRequest);
    return { ...newRequest, id: docRef.id } as LeaveRequest;
  } catch (error) {
    console.error('Error submitting leave request:', error);
    throw error;
  }
};

// Pending approval services
export const getPendingApprovals = async (): Promise<PendingApproval[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, 'pendingApprovals'));
    return querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }) as PendingApproval);
  } catch (error) {
    console.error('Error getting pending approvals:', error);
    return [];
  }
};

export const addPendingApproval = async (approval: Omit<PendingApproval, 'id'>): Promise<PendingApproval> => {
  try {
    const docRef = await addDoc(collection(db, 'pendingApprovals'), approval);
    return { ...approval, id: docRef.id };
  } catch (error) {
    console.error('Error adding pending approval:', error);
    throw error;
  }
};

export const approveRequest = async (approvalId: string): Promise<void> => {
  try {
    // Get the approval
    const approvalDoc = await getDoc(doc(db, 'pendingApprovals', approvalId));
    
    if (approvalDoc.exists()) {
      const approval = { ...approvalDoc.data(), id: approvalDoc.id } as PendingApproval;
      
      if (approval.type === 'leave' && approval.data) {
        // Update leave request status
        await updateDoc(doc(db, 'leaveRequests', approval.data.id), { status: 'approved' });
        
        // Update employee status
        const employeeDoc = await getDoc(doc(db, 'employees', approval.employeeId));
        if (employeeDoc.exists()) {
          await updateDoc(doc(db, 'employees', approval.employeeId), { status: 'On Leave' });
        }
      } else if (approval.type === 'check-in') {
        // Create attendance record
        await addAttendanceRecord({
          employeeId: approval.employeeId,
          employeeName: approval.employeeName,
          date: new Date().toISOString().split('T')[0],
          checkIn: approval.time,
          status: 'Present',
          avatar: approval.avatar
        });
      }
      
      // Remove from pending approvals
      await deleteDoc(doc(db, 'pendingApprovals', approvalId));
    }
  } catch (error) {
    console.error('Error approving request:', error);
    throw error;
  }
};

export const rejectRequest = async (approvalId: string): Promise<void> => {
  try {
    // Get the approval
    const approvalDoc = await getDoc(doc(db, 'pendingApprovals', approvalId));
    
    if (approvalDoc.exists()) {
      const approval = { ...approvalDoc.data(), id: approvalDoc.id } as PendingApproval;
      
      if (approval.type === 'leave' && approval.data) {
        // Update leave request status
        await updateDoc(doc(db, 'leaveRequests', approval.data.id), { status: 'rejected' });
      } else if (approval.type === 'check-in') {
        // Update employee status
        await updateDoc(doc(db, 'employees', approval.employeeId), { status: 'Absent' });
      }
      
      // Remove from pending approvals
      await deleteDoc(doc(db, 'pendingApprovals', approvalId));
    }
  } catch (error) {
    console.error('Error rejecting request:', error);
    throw error;
  }
};