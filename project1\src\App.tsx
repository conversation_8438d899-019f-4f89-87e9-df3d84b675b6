import React, { useState, useEffect } from 'react';
import LandingPage from './components/LandingPage';
import LoginPage from './components/LoginPage';
import AdminDashboard from './components/AdminDashboard';
import EmployeeDashboard from './components/EmployeeDashboard';
import SupervisorDashboard from './components/SupervisorDashboard';
import BusinessDashboard from './components/BusinessDashboard';
import FirebaseInitializer from './components/FirebaseInitializer';

export type UserRole = 'admin' | 'employee' | 'supervisor' | 'business' | 'secretary';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  employeeId?: string;
  companyId?: string;
  department?: string;
  supervisorId?: string;
}

export interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  employeeId: string;
  department: string;
  role: UserRole;
  jobTitle: string;
  avatar: string;
  status: 'Present' | 'Absent' | 'On Leave';
  checkInTime?: string;
  checkOutTime?: string;
  supervisorId?: string;
  joinDate: string;
  address?: string;
  dateOfBirth?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  salary?: string;
  bloodGroup?: string;
  nationality?: string;
  maritalStatus?: string;
  qualification?: string;
  experience?: string;
  workLocation?: string;
  employmentType?: string;
}

export interface AttendanceRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  checkIn?: string;
  checkOut?: string;
  status: 'Present' | 'Absent' | 'Late' | 'Pending';
  hours?: number;
  approvedBy?: string;
  avatar?: string;
}

export interface LeaveRequest {
  id: string;
  employeeId: string;
  employeeName: string;
  type: 'sick' | 'vacation' | 'personal' | 'emergency';
  startDate: string;
  endDate: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  appliedDate: string;
  supervisorId?: string;
  avatar?: string;
  days: number;
}

export interface PendingApproval {
  id: string;
  employeeId: string;
  employeeName: string;
  type: 'check-in' | 'check-out' | 'leave';
  time: string;
  reason: string;
  status: 'pending';
  avatar: string;
  data?: any;
}

// Global state management
export class AppState {
  private static instance: AppState;
  private employees: Employee[] = [];
  private attendanceRecords: AttendanceRecord[] = [];
  private leaveRequests: LeaveRequest[] = [];
  private pendingApprovals: PendingApproval[] = [];
  private listeners: (() => void)[] = [];

  static getInstance(): AppState {
    if (!AppState.instance) {
      AppState.instance = new AppState();
      AppState.instance.initializeData();
    }
    return AppState.instance;
  }

  subscribe(listener: () => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notify() {
    this.listeners.forEach(listener => listener());
  }

  private initializeData() {
    // Initialize with sample data including supervisors and secretaries
    this.employees = [
      
        
    ];

    ;
  }

  // Employee methods
  getEmployees(): Employee[] {
    return [...this.employees];
  }

  // Get employees by role
  getEmployeesByRole(role: UserRole): Employee[] {
    return this.employees.filter(emp => emp.role === role);
  }

  // Get employees under a specific supervisor
  getEmployeesBySupervisor(supervisorId: string): Employee[] {
    return this.employees.filter(emp => emp.supervisorId === supervisorId);
  }

  // Get all supervisors
  getSupervisors(): Employee[] {
    return this.employees.filter(emp => emp.role === 'supervisor');
  }

  // Get all secretaries
  getSecretaries(): Employee[] {
    return this.employees.filter(emp => emp.role === 'secretary');
  }

  addEmployee(employee: Omit<Employee, 'id'>): Employee {
    const newEmployee: Employee = {
      ...employee,
      id: Date.now().toString()
    };
    this.employees.push(newEmployee);
    this.notify();
    return newEmployee;
  }

  updateEmployee(id: string, updates: Partial<Employee>): void {
    const index = this.employees.findIndex(emp => emp.id === id);
    if (index !== -1) {
      this.employees[index] = { ...this.employees[index], ...updates };
      this.notify();
    }
  }

  deleteEmployee(id: string): void {
    this.employees = this.employees.filter(emp => emp.id !== id);
    this.notify();
  }

  // Attendance methods
  getAttendanceRecords(): AttendanceRecord[] {
    return [...this.attendanceRecords];
  }

  // Get attendance records for employees under a supervisor
  getAttendanceRecordsBySupervisor(supervisorId: string): AttendanceRecord[] {
    const supervisedEmployees = this.getEmployeesBySupervisor(supervisorId);
    const supervisedEmployeeIds = supervisedEmployees.map(emp => emp.id);
    return this.attendanceRecords.filter(record => supervisedEmployeeIds.includes(record.employeeId));
  }

  addAttendanceRecord(record: Omit<AttendanceRecord, 'id'>): AttendanceRecord {
    const newRecord: AttendanceRecord = {
      ...record,
      id: Date.now().toString()
    };
    this.attendanceRecords.push(newRecord);
    this.notify();
    return newRecord;
  }

  checkIn(employeeId: string): void {
    const employee = this.employees.find(emp => emp.id === employeeId);
    if (employee) {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit', 
        hour12: true 
      });

      // Create pending approval
      const approval: PendingApproval = {
        id: Date.now().toString(),
        employeeId,
        employeeName: employee.name,
        type: 'check-in',
        time: timeString,
        reason: 'Normal check-in',
        status: 'pending',
        avatar: employee.avatar
      };

      this.pendingApprovals.push(approval);
      
      // Update employee status
      this.updateEmployee(employeeId, { 
        status: 'Present', 
        checkInTime: timeString 
      });

      this.notify();
    }
  }

  checkOut(employeeId: string): void {
    const employee = this.employees.find(emp => emp.id === employeeId);
    if (employee) {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit', 
        hour12: true 
      });

      this.updateEmployee(employeeId, { checkOutTime: timeString });
      this.notify();
    }
  }

  // Leave methods
  getLeaveRequests(): LeaveRequest[] {
    return [...this.leaveRequests];
  }

  // Get leave requests for employees under a supervisor
  getLeaveRequestsBySupervisor(supervisorId: string): LeaveRequest[] {
    const supervisedEmployees = this.getEmployeesBySupervisor(supervisorId);
    const supervisedEmployeeIds = supervisedEmployees.map(emp => emp.id);
    return this.leaveRequests.filter(request => supervisedEmployeeIds.includes(request.employeeId));
  }

  submitLeaveRequest(request: Omit<LeaveRequest, 'id' | 'appliedDate' | 'status'>): LeaveRequest {
    const newRequest: LeaveRequest = {
      ...request,
      id: Date.now().toString(),
      appliedDate: new Date().toISOString().split('T')[0],
      status: 'pending'
    };

    this.leaveRequests.push(newRequest);

    // Add to pending approvals
    const approval: PendingApproval = {
      id: Date.now().toString(),
      employeeId: request.employeeId,
      employeeName: request.employeeName,
      type: 'leave',
      time: new Date().toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit', 
        hour12: true 
      }),
      reason: `${request.type} leave for ${request.days} day(s)`,
      status: 'pending',
      avatar: request.avatar || '',
      data: newRequest
    };

    this.pendingApprovals.push(approval);
    this.notify();
    return newRequest;
  }

  // Approval methods
  getPendingApprovals(): PendingApproval[] {
    return [...this.pendingApprovals];
  }

  // Get pending approvals for employees under a supervisor
  getPendingApprovalsBySupervisor(supervisorId: string): PendingApproval[] {
    const supervisedEmployees = this.getEmployeesBySupervisor(supervisorId);
    const supervisedEmployeeIds = supervisedEmployees.map(emp => emp.id);
    return this.pendingApprovals.filter(approval => supervisedEmployeeIds.includes(approval.employeeId));
  }

  approveRequest(approvalId: string): void {
    const approval = this.pendingApprovals.find(app => app.id === approvalId);
    if (approval) {
      if (approval.type === 'leave' && approval.data) {
        // Update leave request status
        const leaveIndex = this.leaveRequests.findIndex(req => req.id === approval.data.id);
        if (leaveIndex !== -1) {
          this.leaveRequests[leaveIndex].status = 'approved';
          // Update employee status if leave is approved
          this.updateEmployee(approval.employeeId, { status: 'On Leave' });
        }
      } else if (approval.type === 'check-in') {
        // Create attendance record
        const employee = this.employees.find(emp => emp.id === approval.employeeId);
        if (employee) {
          this.addAttendanceRecord({
            employeeId: approval.employeeId,
            employeeName: approval.employeeName,
            date: new Date().toISOString().split('T')[0],
            checkIn: approval.time,
            status: 'Present',
            avatar: approval.avatar
          });
        }
      }

      // Remove from pending approvals
      this.pendingApprovals = this.pendingApprovals.filter(app => app.id !== approvalId);
      this.notify();
    }
  }

  rejectRequest(approvalId: string): void {
    const approval = this.pendingApprovals.find(app => app.id === approvalId);
    if (approval) {
      if (approval.type === 'leave' && approval.data) {
        // Update leave request status
        const leaveIndex = this.leaveRequests.findIndex(req => req.id === approval.data.id);
        if (leaveIndex !== -1) {
          this.leaveRequests[leaveIndex].status = 'rejected';
        }
      } else if (approval.type === 'check-in') {
        // Update employee status back to absent
        this.updateEmployee(approval.employeeId, { status: 'Absent' });
      }

      // Remove from pending approvals
      this.pendingApprovals = this.pendingApprovals.filter(app => app.id !== approvalId);
      this.notify();
    }
  }
}

function App() {
  const [currentPage, setCurrentPage] = useState<'landing' | 'login' | 'dashboard'>('landing');
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for existing user session on app load
  useEffect(() => {
    const checkAuthState = async () => {
      try {
        // Import Firebase auth
        const { auth } = await import('./firebase');
        
        // Set up auth state listener
        const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
          if (firebaseUser) {
            // User is signed in
            try {
              // Get user data from Firestore
              const { db } = await import('./firebase');
              const { doc, getDoc } = await import('firebase/firestore');
              const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
              
              if (userDoc.exists()) {
                const userData = userDoc.data() as Omit<User, 'id'>;
                setUser({ ...userData, id: firebaseUser.uid });
                setCurrentPage('dashboard');
              } else {
                // User exists in Auth but not in Firestore
                console.error('User document not found in Firestore');
              }
            } catch (error) {
              console.error('Error fetching user data:', error);
            }
          } else {
            // User is signed out
            setUser(null);
            setCurrentPage('landing');
          }
          setIsLoading(false);
        });
        
        // Clean up subscription
        return () => unsubscribe();
      } catch (error) {
        console.error('Auth state check error:', error);
        setIsLoading(false);
      }
    };
    
    checkAuthState();
  }, []);

  const handleLogin = (userData: User) => {
    setUser(userData);
    setCurrentPage('dashboard');
  };

  const handleLogout = async () => {
    try {
      // Import the Firebase service
      const { logoutUser } = await import('./services/firebaseService');
      await logoutUser();
      
      // Auth state listener will handle the rest
    } catch (error) {
      console.error('Logout error:', error);
      // Fallback in case Firebase logout fails
      setUser(null);
      setCurrentPage('landing');
    }
  };

  const handleGetStarted = () => {
    setCurrentPage('login');
  };

  const renderDashboard = () => {
    if (!user) return null;

    switch (user.role) {
      case 'admin':
        return <AdminDashboard user={user} onLogout={handleLogout} />;
      case 'employee':
      case 'secretary':
        return <EmployeeDashboard user={user} onLogout={handleLogout} />;
      case 'supervisor':
        return <SupervisorDashboard user={user} onLogout={handleLogout} />;
      case 'business':
        return <BusinessDashboard user={user} onLogout={handleLogout} />;
      default:
        return null;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {currentPage === 'landing' && <LandingPage onGetStarted={handleGetStarted} />}
      {currentPage === 'login' && <LoginPage onLogin={handleLogin} />}
      {currentPage === 'dashboard' && renderDashboard()}
      
      {/* Only show in development mode */}
      {process.env.NODE_ENV === 'development' && <FirebaseInitializer />}
    </div>
  );
}

export default App;