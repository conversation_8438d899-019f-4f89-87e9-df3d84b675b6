import { collection, doc, setDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { db, auth } from '../firebase';
import { Employee, User } from '../App';

// This script is for initializing Firebase with sample data
// You would run this once to set up your database

export const initializeFirebaseData = async () => {
  try {
    console.log('Initializing Firebase data...');
    
    // Create admin user in Authentication
    const adminEmail = '<EMAIL>';
    const adminPassword = 'password123';
    
    let adminUid;
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
      adminUid = userCredential.user.uid;
      console.log('Admin user created in Authentication');
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('Admin user already exists in Authentication');
        // In a real app, you would handle this differently
        adminUid = 'admin-uid'; // Placeholder
      } else {
        throw error;
      }
    }
    
    // Create admin user in Firestore
    const adminUser: Omit<User, 'id'> = {
      name: '<PERSON>',
      email: adminEmail,
      role: 'admin',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      phone: '+1234567890',
      employeeId: 'ADM001',
      companyId: 'COMP001'
    };
    
    await setDoc(doc(db, 'users', adminUid), adminUser);
    console.log('Admin user created in Firestore');
    
    // Create sample employees
    const employees: Omit<Employee, 'id'>[] = [
      {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+1234567891',
        employeeId: 'EMP001',
        department: 'Marketing',
        role: 'employee',
        jobTitle: 'Marketing Specialist',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616c8e6f3b?w=150&h=150&fit=crop&crop=face',
        status: 'Present',
        joinDate: '2023-01-15'
      },
      {
        name: 'Mike Davis',
        email: '<EMAIL>',
        phone: '+1234567892',
        employeeId: 'SUP001',
        department: 'Engineering',
        role: 'supervisor',
        jobTitle: 'Engineering Manager',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7ef0ba?w=150&h=150&fit=crop&crop=face',
        status: 'Present',
        joinDate: '2022-06-10'
      },
      {
        name: 'Lisa Chen',
        email: '<EMAIL>',
        phone: '+1234567893',
        employeeId: 'SEC001',
        department: 'Administration',
        role: 'secretary',
        jobTitle: 'Executive Assistant',
        avatar: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',
        status: 'Absent',
        joinDate: '2023-03-22'
      }
    ];
    
    // Add employees to Firestore
    for (const employee of employees) {
      const docRef = doc(collection(db, 'employees'));
      await setDoc(docRef, employee);
      console.log(`Employee ${employee.name} created`);
      
      // Also create user accounts for employees
      const userEmail = employee.email;
      const userPassword = 'password123'; // In a real app, you would use a secure method
      
      try {
        const userCredential = await createUserWithEmailAndPassword(auth, userEmail, userPassword);
        const uid = userCredential.user.uid;
        
        // Create user in Firestore
        const user: Omit<User, 'id'> = {
          name: employee.name,
          email: employee.email,
          role: employee.role,
          avatar: employee.avatar,
          phone: employee.phone,
          employeeId: employee.employeeId,
          companyId: 'COMP001'
        };
        
        await setDoc(doc(db, 'users', uid), user);
        console.log(`User account created for ${employee.name}`);
      } catch (error: any) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`User account already exists for ${employee.name}`);
        } else {
          console.error(`Error creating user account for ${employee.name}:`, error);
        }
      }
    }
    
    console.log('Firebase data initialization complete!');
  } catch (error) {
    console.error('Error initializing Firebase data:', error);
  }
};

// Uncomment to run the initialization
// initializeFirebaseData();