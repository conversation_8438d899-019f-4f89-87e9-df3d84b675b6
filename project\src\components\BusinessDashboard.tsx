import React, { useState } from 'react';
import { 
  Building2, 
  TrendingUp, 
  Users, 
  DollarSign, 
  BarChart3, 
  <PERSON>Chart, 
  Calendar, 
  Bell, 
  LogOut,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { User } from '../App';

interface BusinessDashboardProps {
  user: User;
  onLogout: () => void;
}

const BusinessDashboard: React.FC<BusinessDashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  const businessStats = [
    { label: 'Total Companies', value: '8', change: '+2', trend: 'up', color: 'bg-blue-100 text-blue-600' },
    { label: 'Total Employees', value: '1,247', change: '+5.2%', trend: 'up', color: 'bg-emerald-100 text-emerald-600' },
    { label: 'Monthly Revenue', value: '$125K', change: '+12.3%', trend: 'up', color: 'bg-purple-100 text-purple-600' },
    { label: 'Avg. Attendance', value: '94.2%', change: '+2.1%', trend: 'up', color: 'bg-yellow-100 text-yellow-600' }
  ];

  const companyData = [
    { id: 1, name: 'TechCorp Inc.', employees: 245, attendance: '96%', revenue: '$45K', growth: '+8.5%' },
    { id: 2, name: 'Digital Solutions', employees: 189, attendance: '94%', revenue: '$32K', growth: '+12.1%' },
    { id: 3, name: 'Innovation Labs', employees: 156, attendance: '92%', revenue: '$28K', growth: '+6.8%' },
    { id: 4, name: 'StartupCo', employees: 98, attendance: '91%', revenue: '$15K', growth: '+18.2%' }
  ];

  const monthlyData = [
    { month: 'Jan', revenue: 95, employees: 1150, attendance: 92 },
    { month: 'Feb', revenue: 105, employees: 1180, attendance: 94 },
    { month: 'Mar', revenue: 115, employees: 1200, attendance: 93 },
    { month: 'Apr', revenue: 125, employees: 1247, attendance: 94 }
  ];

  const departmentStats = [
    { name: 'Engineering', percentage: 35, color: 'bg-blue-500' },
    { name: 'Sales', percentage: 22, color: 'bg-emerald-500' },
    { name: 'Marketing', percentage: 18, color: 'bg-purple-500' },
    { name: 'HR', percentage: 12, color: 'bg-yellow-500' },
    { name: 'Operations', percentage: 13, color: 'bg-red-500' }
  ];

  const menuItems = [
    { id: 'overview', label: 'Overview', icon: <Building2 className="w-5 h-5" /> },
    { id: 'analytics', label: 'Analytics', icon: <BarChart3 className="w-5 h-5" /> },
    { id: 'companies', label: 'Companies', icon: <Building2 className="w-5 h-5" /> },
    { id: 'financial', label: 'Financial', icon: <DollarSign className="w-5 h-5" /> },
    { id: 'reports', label: 'Reports', icon: <PieChart className="w-5 h-5" /> }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {businessStats.map((stat, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${stat.color}`}>
                      <TrendingUp className="w-6 h-6" />
                    </div>
                    <div className="flex items-center space-x-1">
                      {stat.trend === 'up' ? (
                        <ArrowUp className="w-4 h-4 text-emerald-600" />
                      ) : (
                        <ArrowDown className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm font-medium ${
                        stat.trend === 'up' ? 'text-emerald-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
                  <p className="text-gray-600">{stat.label}</p>
                </div>
              ))}
            </div>

            {/* Performance Chart */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Performance Overview</h3>
                <select 
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                </select>
              </div>
              <div className="h-64 flex items-end justify-between space-x-2">
                {monthlyData.map((data, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div className="w-full bg-gray-200 rounded-t-lg overflow-hidden mb-2">
                      <div 
                        className="bg-blue-500 transition-all duration-500 ease-out"
                        style={{ height: `${(data.revenue / 150) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600">{data.month}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Department Distribution */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Department Distribution</h3>
              <div className="space-y-4">
                {departmentStats.map((dept, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 rounded-full ${dept.color}`} />
                      <span className="text-gray-700">{dept.name}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${dept.color}`}
                          style={{ width: `${dept.percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-8">
                        {dept.percentage}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'companies':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Company Performance</h2>
              <div className="flex space-x-2">
                <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  Export
                </button>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Add Company
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {companyData.map((company) => (
                <div key={company.id} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{company.name}</h3>
                    <span className="text-sm font-medium text-emerald-600">{company.growth}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{company.employees}</p>
                      <p className="text-sm text-gray-600">Employees</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{company.attendance}</p>
                      <p className="text-sm text-gray-600">Attendance</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{company.revenue}</p>
                      <p className="text-sm text-gray-600">Revenue</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'analytics':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Business Analytics</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
                <div className="h-48 flex items-end justify-between space-x-2">
                  {monthlyData.map((data, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-t-lg overflow-hidden mb-2">
                        <div 
                          className="bg-purple-500 transition-all duration-500 ease-out"
                          style={{ height: `${(data.revenue / 150) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-600">{data.month}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Employee Growth</h3>
                <div className="h-48 flex items-end justify-between space-x-2">
                  {monthlyData.map((data, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-t-lg overflow-hidden mb-2">
                        <div 
                          className="bg-emerald-500 transition-all duration-500 ease-out"
                          style={{ height: `${((data.employees - 1000) / 300) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-600">{data.month}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
            <p className="text-gray-600">This feature is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Building2 className="w-8 h-8 text-purple-600" />
                <span className="text-xl font-bold text-gray-900">Business Dashboard</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <div className="flex items-center space-x-3">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-8 h-8 rounded-full"
                />
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-600">Business Manager</p>
                </div>
              </div>
              <button
                onClick={onLogout}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
          <nav className="p-4">
            <div className="space-y-2">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === item.id
                      ? 'bg-purple-50 text-purple-600 border border-purple-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default BusinessDashboard;