import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Bell, 
  LogOut, 
  Building2,
  Calendar,
  Filter,
  Search,
  Crown,
  UserCheck
} from 'lucide-react';
import { User, AppState, Employee, PendingApproval, LeaveRequest, AttendanceRecord } from '../App';

interface SupervisorDashboardProps {
  user: User;
  onLogout: () => void;
}

const SupervisorDashboard: React.FC<SupervisorDashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [myTeam, setMyTeam] = useState<Employee[]>([]);
  const [myPendingApprovals, setMyPendingApprovals] = useState<PendingApproval[]>([]);
  const [myLeaveRequests, setMyLeaveRequests] = useState<LeaveRequest[]>([]);
  const [myAttendanceRecords, setMyAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  const appState = AppState.getInstance();

  useEffect(() => {
    const updateData = () => {
      // Get only employees under this supervisor
      const teamMembers = appState.getEmployeesBySupervisor(user.id || '');
      const teamApprovals = appState.getPendingApprovalsBySupervisor(user.id || '');
      const teamLeaves = appState.getLeaveRequestsBySupervisor(user.id || '');
      const teamAttendance = appState.getAttendanceRecordsBySupervisor(user.id || '');
      
      setMyTeam(teamMembers);
      setMyPendingApprovals(teamApprovals);
      setMyLeaveRequests(teamLeaves);
      setMyAttendanceRecords(teamAttendance);
    };

    updateData();
    const unsubscribe = appState.subscribe(updateData);
    return unsubscribe;
  }, [user.id]);

  const stats = [
    { 
      label: 'My Team Members', 
      value: myTeam.length.toString(), 
      color: 'bg-blue-100 text-blue-600',
      icon: <Users className="w-6 h-6" />
    },
    { 
      label: 'Present Today', 
      value: myTeam.filter(emp => emp.status === 'Present').length.toString(), 
      color: 'bg-emerald-100 text-emerald-600',
      icon: <CheckCircle className="w-6 h-6" />
    },
    { 
      label: 'Pending Approvals', 
      value: myPendingApprovals.length.toString(), 
      color: 'bg-yellow-100 text-yellow-600',
      icon: <AlertTriangle className="w-6 h-6" />
    },
    { 
      label: 'On Leave', 
      value: myTeam.filter(emp => emp.status === 'On Leave').length.toString(), 
      color: 'bg-red-100 text-red-600',
      icon: <Calendar className="w-6 h-6" />
    }
  ];

  const menuItems = [
    { id: 'overview', label: 'Overview', icon: <Building2 className="w-5 h-5" /> },
    { id: 'team', label: 'My Team', icon: <Users className="w-5 h-5" /> },
    { id: 'approvals', label: 'Pending Approvals', icon: <AlertTriangle className="w-5 h-5" /> },
    { id: 'attendance', label: 'Team Attendance', icon: <Clock className="w-5 h-5" /> },
    { id: 'leaves', label: 'Leave Requests', icon: <Calendar className="w-5 h-5" /> }
  ];

  const handleApproval = (id: string, action: 'approve' | 'reject') => {
    if (action === 'approve') {
      appState.approveRequest(id);
    } else {
      appState.rejectRequest(id);
    }
  };

  const filteredTeam = myTeam.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.jobTitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${stat.color}`}>
                      {stat.icon}
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
                  <p className="text-gray-600">{stat.label}</p>
                </div>
              ))}
            </div>

            {/* Team Overview */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">My Team Overview</h3>
                <div className="flex items-center space-x-2">
                  <Crown className="w-5 h-5 text-purple-600" />
                  <span className="text-sm text-gray-600">Supervisor: {user.name}</span>
                </div>
              </div>
              
              {myTeam.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {myTeam.slice(0, 6).map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex items-center space-x-3">
                        <img
                          src={member.avatar}
                          alt={member.name}
                          className="w-10 h-10 rounded-full"
                        />
                        <div>
                          <p className="font-medium text-gray-900">{member.name}</p>
                          <p className="text-sm text-gray-600">{member.jobTitle}</p>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            member.role === 'secretary' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                          }`}>
                            {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          member.status === 'Present' ? 'bg-emerald-100 text-emerald-600' :
                          member.status === 'On Leave' ? 'bg-yellow-100 text-yellow-600' :
                          'bg-red-100 text-red-600'
                        }`}>
                          {member.status}
                        </span>
                        <p className="text-xs text-gray-500 mt-1">{member.checkInTime || '-'}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>No team members assigned</p>
                  <p className="text-sm">Contact admin to assign employees to your supervision</p>
                </div>
              )}
            </div>

            {/* Pending Approvals */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Pending Approvals</h3>
                <span className="bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full text-sm font-medium">
                  {myPendingApprovals.length} pending
                </span>
              </div>
              <div className="space-y-3">
                {myPendingApprovals.slice(0, 3).map((approval) => (
                  <div key={approval.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="flex items-center space-x-3">
                      <img
                        src={approval.avatar}
                        alt={approval.employeeName}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <p className="font-medium text-gray-900">{approval.employeeName}</p>
                        <p className="text-sm text-gray-600">
                          {approval.type.charAt(0).toUpperCase() + approval.type.slice(1)} • {approval.time}
                        </p>
                        <p className="text-xs text-gray-500">{approval.reason}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleApproval(approval.id, 'approve')}
                        className="p-2 text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors"
                        title="Approve"
                      >
                        <CheckCircle className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleApproval(approval.id, 'reject')}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Reject"
                      >
                        <XCircle className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                ))}
                {myPendingApprovals.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>No pending approvals</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'approvals':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Pending Approvals</h2>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Crown className="w-4 h-4" />
                <span>Only showing approvals for your team</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6">
                <div className="space-y-4">
                  {myPendingApprovals.map((approval) => (
                    <div key={approval.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <img
                          src={approval.avatar}
                          alt={approval.employeeName}
                          className="w-12 h-12 rounded-full"
                        />
                        <div>
                          <p className="font-medium text-gray-900">{approval.employeeName}</p>
                          <p className="text-sm text-gray-600">
                            {approval.type.charAt(0).toUpperCase() + approval.type.slice(1)} request at {approval.time}
                          </p>
                          <p className="text-sm text-gray-500">{approval.reason}</p>
                        </div>
                      </div>
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleApproval(approval.id, 'approve')}
                          className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2"
                        >
                          <CheckCircle className="w-4 h-4" />
                          <span>Approve</span>
                        </button>
                        <button
                          onClick={() => handleApproval(approval.id, 'reject')}
                          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                        >
                          <XCircle className="w-4 h-4" />
                          <span>Reject</span>
                        </button>
                      </div>
                    </div>
                  ))}
                  {myPendingApprovals.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <CheckCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                      <p>No pending approvals from your team</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'team':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">My Team</h2>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Crown className="w-4 h-4" />
                  <span>{myTeam.length} team member{myTeam.length !== 1 ? 's' : ''}</span>
                </div>
                <div className="relative">
                  <Search className="w-5 h-5 absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search team members..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-4 font-medium text-gray-900">Employee</th>
                      <th className="text-left p-4 font-medium text-gray-900">Role</th>
                      <th className="text-left p-4 font-medium text-gray-900">Job Title</th>
                      <th className="text-left p-4 font-medium text-gray-900">Status</th>
                      <th className="text-left p-4 font-medium text-gray-900">Check-in</th>
                      <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredTeam.map((member) => (
                      <tr key={member.id} className="hover:bg-gray-50 transition-colors">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <img
                              src={member.avatar}
                              alt={member.name}
                              className="w-10 h-10 rounded-full"
                            />
                            <div>
                              <span className="font-medium text-gray-900">{member.name}</span>
                              <p className="text-sm text-gray-500">{member.employeeId}</p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            member.role === 'secretary' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                          }`}>
                            {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                          </span>
                        </td>
                        <td className="p-4 text-gray-600">{member.jobTitle}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            member.status === 'Present' ? 'bg-emerald-100 text-emerald-600' :
                            member.status === 'On Leave' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {member.status}
                          </span>
                        </td>
                        <td className="p-4 text-gray-600">{member.checkInTime || '-'}</td>
                        <td className="p-4">
                          <button className="text-blue-600 hover:text-blue-700 font-medium transition-colors">
                            View Details
                          </button>
                        </td>
                      </tr>
                    ))}
                    {filteredTeam.length === 0 && (
                      <tr>
                        <td colSpan={6} className="p-8 text-center text-gray-500">
                          <Users className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>No team members found</p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );

      case 'leaves':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Team Leave Requests</h2>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Crown className="w-4 h-4" />
                <span>Only showing requests from your team</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6">
                <div className="space-y-4">
                  {myLeaveRequests.map((leave) => (
                    <div key={leave.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <img
                            src={leave.avatar}
                            alt={leave.employeeName}
                            className="w-10 h-10 rounded-full"
                          />
                          <div>
                            <p className="font-medium text-gray-900">{leave.employeeName}</p>
                            <p className="text-sm text-gray-600">
                              {leave.type.charAt(0).toUpperCase() + leave.type.slice(1)} Leave
                            </p>
                            <p className="text-sm text-gray-500">
                              {leave.startDate} to {leave.endDate} • {leave.days} day{leave.days > 1 ? 's' : ''}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">{leave.reason}</p>
                          </div>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          leave.status === 'approved' ? 'bg-emerald-100 text-emerald-600' :
                          leave.status === 'rejected' ? 'bg-red-100 text-red-600' :
                          'bg-yellow-100 text-yellow-600'
                        }`}>
                          {leave.status.charAt(0).toUpperCase() + leave.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  ))}
                  {myLeaveRequests.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Calendar className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                      <p>No leave requests from your team</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'attendance':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Team Attendance</h2>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Crown className="w-4 h-4" />
                <span>Only showing attendance for your team</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-4 font-medium text-gray-900">Employee</th>
                      <th className="text-left p-4 font-medium text-gray-900">Date</th>
                      <th className="text-left p-4 font-medium text-gray-900">Check In</th>
                      <th className="text-left p-4 font-medium text-gray-900">Check Out</th>
                      <th className="text-left p-4 font-medium text-gray-900">Hours</th>
                      <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {myAttendanceRecords.map((record) => (
                      <tr key={record.id} className="hover:bg-gray-50 transition-colors">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <img
                              src={record.avatar}
                              alt={record.employeeName}
                              className="w-8 h-8 rounded-full"
                            />
                            <span className="font-medium text-gray-900">{record.employeeName}</span>
                          </div>
                        </td>
                        <td className="p-4 text-gray-600">{record.date}</td>
                        <td className="p-4 text-gray-600">{record.checkIn || '-'}</td>
                        <td className="p-4 text-gray-600">{record.checkOut || '-'}</td>
                        <td className="p-4 text-gray-600">{record.hours || 0}h</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            record.status === 'Present' ? 'bg-emerald-100 text-emerald-600' :
                            record.status === 'Late' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {record.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                    {myAttendanceRecords.length === 0 && (
                      <tr>
                        <td colSpan={6} className="p-8 text-center text-gray-500">
                          <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>No attendance records for your team</p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
            <p className="text-gray-600">This feature is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Building2 className="w-8 h-8 text-emerald-600" />
                <span className="text-xl font-bold text-gray-900">Supervisor Dashboard</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="w-5 h-5" />
                {myPendingApprovals.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {myPendingApprovals.length}
                  </span>
                )}
              </button>
              <div className="flex items-center space-x-3">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-8 h-8 rounded-full"
                />
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-600">Supervisor</p>
                </div>
              </div>
              <button
                onClick={onLogout}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
          <nav className="p-4">
            <div className="space-y-2">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === item.id
                      ? 'bg-emerald-50 text-emerald-600 border border-emerald-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  {item.icon}
                  <span>{item.label}</span>
                  {item.id === 'approvals' && myPendingApprovals.length > 0 && (
                    <span className="ml-auto bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {myPendingApprovals.length}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default SupervisorDashboard;