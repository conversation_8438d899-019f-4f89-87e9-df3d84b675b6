import React, { useState } from 'react';
import { ArrowLeft, Building2, User, Users, Shield, BarChart3, Phone, Mail, Eye, EyeOff } from 'lucide-react';
import { User as UserType } from '../App';

interface LoginPageProps {
  onLogin: (user: UserType) => void;
}

type UserRole = 'admin' | 'employee' | 'supervisor' | 'business';
type LoginMethod = 'phone' | 'email' | 'google';

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [activeTab, setActiveTab] = useState<'employee' | 'other'>('employee');
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [loginMethod, setLoginMethod] = useState<LoginMethod>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (loginMethod === 'phone' && !phoneNumber.trim()) {
      alert('Please enter your phone number');
      return;
    }
    if (loginMethod === 'email' && (!email.trim() || !password.trim())) {
      alert('Please enter your email and password');
      return;
    }

    setIsLoading(true);
    
    try {
      // For phone login, we would need to implement phone authentication
      // For now, we'll focus on email/password authentication
      if (loginMethod === 'email') {
        // Import the Firebase service
        const { loginWithEmailAndPassword } = await import('../services/firebaseService');
        
        // Attempt to login with Firebase
        const user = await loginWithEmailAndPassword(email, password);
        
        // If login is successful, call the onLogin callback
        onLogin(user);
      } else {
        // For phone authentication, we would implement it here
        // For now, we'll use a mock implementation
        setTimeout(() => {
          const mockUser: UserType = {
            id: '1',
            name: selectedRole === 'admin' ? 'John Smith' : 
                  selectedRole === 'employee' ? 'Sarah Johnson' :
                  selectedRole === 'supervisor' ? 'Mike Davis' : 'Lisa Chen',
            email: '<EMAIL>',
            role: selectedRole!,
            avatar: `https://images.unsplash.com/photo-${selectedRole === 'admin' ? '1472099645785-5658abf4ff4e' : 
                                                        selectedRole === 'employee' ? '1494790108755-2616c8e6f3b' :
                                                        selectedRole === 'supervisor' ? '1507003211169-0a1dd7ef0ba' : '1580489944761-15a19d654956'}?w=150&h=150&fit=crop&crop=face`,
            phone: phoneNumber,
            employeeId: selectedRole !== 'business' ? 'EMP001' : undefined,
            companyId: 'COMP001'
          };
          
          onLogin(mockUser);
        }, 1000);
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed. Please check your credentials and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    if (!selectedRole) {
      alert('Please select a role first');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Import the Firebase service
      const { loginWithGoogle } = await import('../services/firebaseService');
      
      // Attempt to login with Google via Firebase
      const user = await loginWithGoogle();
      
      // Update the user role based on selection
      // In a real app, this would be determined by the user's role in the database
      const userWithRole: UserType = {
        ...user,
        role: selectedRole
      };
      
      // If login is successful, call the onLogin callback
      onLogin(userWithRole);
    } catch (error) {
      console.error('Google login error:', error);
      alert('Google login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (selectedRole) {
      setSelectedRole(null);
      setLoginMethod('phone');
      setPhoneNumber('');
      setEmail('');
      setPassword('');
    }
  };

  const renderLoginForm = () => {
    return (
      <div className="space-y-6">
        {/* Login Method Selection */}
        <div className="flex space-x-2 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setLoginMethod('phone')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              loginMethod === 'phone'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Phone
          </button>
          <button
            onClick={() => setLoginMethod('email')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
              loginMethod === 'email'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Email
          </button>
        </div>

        {/* Phone Login */}
        {loginMethod === 'phone' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <div className="relative">
              <Phone className="w-5 h-5 absolute left-3 top-3 text-gray-400" />
              <input
                type="tel"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                placeholder="Enter your phone number"
                required
              />
            </div>
          </div>
        )}

        {/* Email Login */}
        {loginMethod === 'email' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail className="w-5 h-5 absolute left-3 top-3 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-4 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Login Button */}
        <button
          onClick={handleLogin}
          disabled={isLoading || (loginMethod === 'phone' && !phoneNumber.trim()) || (loginMethod === 'email' && (!email.trim() || !password.trim()))}
          className="w-full bg-blue-600 text-white py-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-semibold text-lg"
        >
          {isLoading ? 'Logging in...' : 'Login'}
        </button>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        {/* Google Login */}
        <button
          onClick={handleGoogleLogin}
          disabled={isLoading}
          className="w-full flex items-center justify-center space-x-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          <span className="font-medium text-gray-700">Continue with Google</span>
        </button>

        {/* Footer Links */}
        <div className="text-center">
          <button className="text-blue-600 hover:text-blue-700 transition-colors text-sm">
            Forgot Password?
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md p-8 border border-gray-200">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-6">
            <Building2 className="w-6 h-6 text-blue-600" />
            <span className="text-lg font-semibold text-gray-700">Praskla Technology</span>
          </div>
        </div>

        {!selectedRole && (
          <>
            {/* Tab Navigation */}
            <div className="flex mb-8 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('employee')}
                className={`flex-1 py-3 px-4 rounded-md font-medium transition-all ${
                  activeTab === 'employee'
                    ? 'bg-white text-blue-600 shadow-sm border-b-2 border-blue-600'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Employee
              </button>
              <button
                onClick={() => setActiveTab('other')}
                className={`flex-1 py-3 px-4 rounded-md font-medium transition-all ${
                  activeTab === 'other'
                    ? 'bg-white text-blue-600 shadow-sm border-b-2 border-blue-600'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Other User
              </button>
            </div>

            {/* User Icon */}
            <div className="flex justify-center mb-8">
              <div className="w-24 h-24 bg-gray-600 rounded-full flex items-center justify-center">
                <div className="relative">
                  <User className="w-12 h-12 text-white" />
                  <div className="absolute -right-2 -bottom-1 w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Role Selection */}
            <div className="space-y-3 mb-6">
              {activeTab === 'employee' ? (
                <button
                  onClick={() => setSelectedRole('employee')}
                  className="w-full p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors text-left"
                >
                  <div className="flex items-center space-x-3">
                    <User className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-gray-800">Employee Login</span>
                  </div>
                </button>
              ) : (
                <>
                  <button
                    onClick={() => setSelectedRole('admin')}
                    className="w-full p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <Shield className="w-5 h-5 text-red-600" />
                      <span className="font-medium text-gray-800">Admin Login</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setSelectedRole('supervisor')}
                    className="w-full p-4 bg-emerald-50 border border-emerald-200 rounded-lg hover:bg-emerald-100 transition-colors text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <Users className="w-5 h-5 text-emerald-600" />
                      <span className="font-medium text-gray-800">Supervisor Login</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setSelectedRole('business')}
                    className="w-full p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <BarChart3 className="w-5 h-5 text-purple-600" />
                      <span className="font-medium text-gray-800">Business Login</span>
                    </div>
                  </button>
                </>
              )}
            </div>

            {/* Login Button - Disabled */}
            <button
              disabled
              className="w-full bg-blue-600 text-white py-4 rounded-lg font-semibold text-lg mb-6 opacity-50 cursor-not-allowed"
            >
              Login
            </button>
          </>
        )}

        {selectedRole && (
          <>
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600 hover:text-gray-900 mb-6 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </button>

            <div className="text-center mb-6">
              <p className="text-gray-600">
                Sign in as <span className="font-semibold text-blue-600 capitalize">{selectedRole}</span>
              </p>
            </div>

            {renderLoginForm()}
          </>
        )}
      </div>
    </div>
  );
};

export default LoginPage;