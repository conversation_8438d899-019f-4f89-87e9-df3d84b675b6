import React, { useState, useEffect } from 'react';
import { 
  User as UserIcon, 
  Clock, 
  Calendar, 
  FileText, 
  Bell, 
  LogOut, 
  Building2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  X,
  LogIn,
  LogOut as LogOutIcon
} from 'lucide-react';
import { User, AppState, LeaveRequest, AttendanceRecord } from '../App';

interface EmployeeDashboardProps {
  user: User;
  onLogout: () => void;
}

const EmployeeDashboard: React.FC<EmployeeDashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [leaveForm, setLeaveForm] = useState({
    type: 'sick',
    startDate: '',
    endDate: '',
    reason: ''
  });
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [attendanceHistory, setAttendanceHistory] = useState<AttendanceRecord[]>([]);

  const appState = AppState.getInstance();

  useEffect(() => {
    const updateData = () => {
      const allLeaves = appState.getLeaveRequests();
      const allAttendance = appState.getAttendanceRecords();
      
      // Filter for current user
      setLeaveRequests(allLeaves.filter(leave => leave.employeeId === user.id));
      setAttendanceHistory(allAttendance.filter(record => record.employeeId === user.id));
      
      // Check if user is already checked in today
      const today = new Date().toISOString().split('T')[0];
      const todayRecord = allAttendance.find(record => 
        record.employeeId === user.id && 
        record.date === today && 
        record.status === 'Present'
      );
      setIsCheckedIn(!!todayRecord);
    };

    updateData();
    const unsubscribe = appState.subscribe(updateData);
    return unsubscribe;
  }, [user.id]);

  const todayStats = [
    { label: 'Check-in Time', value: isCheckedIn ? '9:15 AM' : 'Not checked in', status: isCheckedIn ? 'good' : 'pending' },
    { label: 'Work Hours', value: isCheckedIn ? '7.5 hrs' : '0 hrs', status: 'normal' },
    { label: 'Break Time', value: '45 min', status: 'normal' },
    { label: 'Status', value: isCheckedIn ? 'Present' : 'Absent', status: isCheckedIn ? 'good' : 'pending' }
  ];

  const menuItems = [
    { id: 'overview', label: 'Overview', icon: <Building2 className="w-5 h-5" /> },
    { id: 'profile', label: 'My Profile', icon: <UserIcon className="w-5 h-5" /> },
    { id: 'attendance', label: 'Attendance', icon: <Clock className="w-5 h-5" /> },
    { id: 'leaves', label: 'Leave Management', icon: <Calendar className="w-5 h-5" /> },
    { id: 'documents', label: 'Documents', icon: <FileText className="w-5 h-5" /> }
  ];

  const handleCheckIn = () => {
    appState.checkIn(user.id);
    setIsCheckedIn(true);
  };

  const handleCheckOut = () => {
    appState.checkOut(user.id);
    // In a real app, this would update the check-out time
    alert('Check-out successful!');
  };

  const handleLeaveSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (leaveForm.startDate && leaveForm.endDate && leaveForm.reason) {
      const startDate = new Date(leaveForm.startDate);
      const endDate = new Date(leaveForm.endDate);
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      appState.submitLeaveRequest({
        employeeId: user.id,
        employeeName: user.name,
        type: leaveForm.type as 'sick' | 'vacation' | 'personal' | 'emergency',
        startDate: leaveForm.startDate,
        endDate: leaveForm.endDate,
        reason: leaveForm.reason,
        supervisorId: 'supervisor1',
        avatar: user.avatar || '',
        days
      });

      setLeaveForm({ type: 'sick', startDate: '', endDate: '', reason: '' });
      setShowLeaveModal(false);
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={handleCheckIn}
                  disabled={isCheckedIn}
                  className={`p-4 rounded-lg border-2 border-dashed transition-all ${
                    isCheckedIn 
                      ? 'bg-emerald-50 border-emerald-300 text-emerald-600 cursor-not-allowed' 
                      : 'border-gray-300 hover:border-blue-300 hover:bg-blue-50 hover:scale-105'
                  }`}
                >
                  <LogIn className="w-8 h-8 mx-auto mb-2" />
                  <p className="font-medium">
                    {isCheckedIn ? 'Checked In' : 'Check In'}
                  </p>
                  <p className="text-sm text-gray-600">
                    {isCheckedIn ? 'Status: Present' : 'Mark your attendance'}
                  </p>
                </button>
                <button 
                  onClick={handleCheckOut}
                  disabled={!isCheckedIn}
                  className={`p-4 rounded-lg border-2 border-dashed transition-all ${
                    !isCheckedIn 
                      ? 'border-gray-300 text-gray-400 cursor-not-allowed' 
                      : 'border-gray-300 hover:border-red-300 hover:bg-red-50 hover:scale-105'
                  }`}
                >
                  <LogOutIcon className="w-8 h-8 mx-auto mb-2" />
                  <p className="font-medium">Check Out</p>
                  <p className="text-sm text-gray-600">
                    {!isCheckedIn ? 'Check in first' : 'End your work day'}
                  </p>
                </button>
              </div>
            </div>

            {/* Today's Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {todayStats.map((stat, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-600">{stat.label}</h4>
                    <div className={`w-3 h-3 rounded-full ${
                      stat.status === 'good' ? 'bg-emerald-500' :
                      stat.status === 'pending' ? 'bg-yellow-500' :
                      'bg-gray-400'
                    }`} />
                  </div>
                  <p className="text-xl font-bold text-gray-900">{stat.value}</p>
                  {stat.status === 'pending' && !isCheckedIn && (
                    <p className="text-xs text-yellow-600 mt-1">Please check in</p>
                  )}
                </div>
              ))}
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {leaveRequests.slice(0, 3).map((leave) => (
                  <div key={leave.id} className={`flex items-center space-x-3 p-3 rounded-lg ${
                    leave.status === 'pending' ? 'bg-yellow-50' :
                    leave.status === 'approved' ? 'bg-emerald-50' :
                    'bg-red-50'
                  }`}>
                    {leave.status === 'pending' ? (
                      <AlertCircle className="w-5 h-5 text-yellow-600" />
                    ) : leave.status === 'approved' ? (
                      <CheckCircle className="w-5 h-5 text-emerald-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    <div>
                      <p className="font-medium text-gray-900">
                        {leave.type.charAt(0).toUpperCase() + leave.type.slice(1)} Leave {leave.status}
                      </p>
                      <p className="text-sm text-gray-600">
                        {leave.startDate} to {leave.endDate} ({leave.days} day{leave.days > 1 ? 's' : ''})
                      </p>
                    </div>
                  </div>
                ))}
                {leaveRequests.length === 0 && (
                  <div className="text-center py-4 text-gray-500">
                    <p>No recent activity</p>
                  </div>
                )}
              </div>
            </div>

            {/* Leave Application Modal */}
            {showLeaveModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Apply for Leave</h3>
                    <button
                      onClick={() => setShowLeaveModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                  <form onSubmit={handleLeaveSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Leave Type</label>
                      <select
                        value={leaveForm.type}
                        onChange={(e) => setLeaveForm({...leaveForm, type: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="sick">Sick Leave</option>
                        <option value="vacation">Vacation</option>
                        <option value="personal">Personal</option>
                        <option value="emergency">Emergency</option>
                      </select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input
                          type="date"
                          value={leaveForm.startDate}
                          onChange={(e) => setLeaveForm({...leaveForm, startDate: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input
                          type="date"
                          value={leaveForm.endDate}
                          onChange={(e) => setLeaveForm({...leaveForm, endDate: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                      <textarea
                        value={leaveForm.reason}
                        onChange={(e) => setLeaveForm({...leaveForm, reason: e.target.value})}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Please provide a reason for your leave..."
                        required
                      />
                    </div>
                    <div className="flex space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setShowLeaveModal(false)}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Submit Application
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>
        );

      case 'profile':
        return (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">My Profile</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="text-center">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-32 h-32 rounded-full mx-auto mb-4"
                />
                <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
                <p className="text-gray-600">Software Developer</p>
              </div>
              <div className="lg:col-span-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Employee ID</label>
                    <p className="text-gray-900">{user.employeeId}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <p className="text-gray-900">{user.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <p className="text-gray-900">{user.phone}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <p className="text-gray-900">Engineering</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Join Date</label>
                    <p className="text-gray-900">Jan 15, 2023</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Manager</label>
                    <p className="text-gray-900">Mike Davis</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'attendance':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Attendance History</h2>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-4 font-medium text-gray-900">Date</th>
                      <th className="text-left p-4 font-medium text-gray-900">Check In</th>
                      <th className="text-left p-4 font-medium text-gray-900">Check Out</th>
                      <th className="text-left p-4 font-medium text-gray-900">Hours</th>
                      <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {attendanceHistory.map((record, index) => (
                      <tr key={index} className="hover:bg-gray-50 transition-colors">
                        <td className="p-4 font-medium text-gray-900">{record.date}</td>
                        <td className="p-4 text-gray-600">{record.checkIn || '-'}</td>
                        <td className="p-4 text-gray-600">{record.checkOut || '-'}</td>
                        <td className="p-4 text-gray-600">{record.hours || 0}h</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            record.status === 'Present' ? 'bg-emerald-100 text-emerald-600' :
                            record.status === 'Late' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {record.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                    {attendanceHistory.length === 0 && (
                      <tr>
                        <td colSpan={5} className="p-8 text-center text-gray-500">
                          <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>No attendance records found</p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );

      case 'leaves':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Leave Management</h2>
              <button 
                onClick={() => setShowLeaveModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Apply Leave</span>
              </button>
            </div>

            {/* Leave History */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Leave History</h3>
              <div className="space-y-3">
                {leaveRequests.map((leave) => (
                  <div key={leave.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div>
                      <p className="font-medium text-gray-900">
                        {leave.type.charAt(0).toUpperCase() + leave.type.slice(1)} Leave
                      </p>
                      <p className="text-sm text-gray-600">
                        {leave.startDate} to {leave.endDate} • {leave.days} day{leave.days > 1 ? 's' : ''}
                      </p>
                      <p className="text-xs text-gray-500">{leave.reason}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      leave.status === 'approved' ? 'bg-emerald-100 text-emerald-600' :
                      leave.status === 'rejected' ? 'bg-red-100 text-red-600' :
                      'bg-yellow-100 text-yellow-600'
                    }`}>
                      {leave.status.charAt(0).toUpperCase() + leave.status.slice(1)}
                    </span>
                  </div>
                ))}
                {leaveRequests.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>No leave requests found</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
            <p className="text-gray-600">This feature is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Building2 className="w-8 h-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">Employee Portal</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Apply Leave Button in Header */}
              <button 
                onClick={() => setShowLeaveModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Calendar className="w-4 h-4" />
                <span>Apply Leave</span>
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <div className="flex items-center space-x-3">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-8 h-8 rounded-full"
                />
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-600">Employee</p>
                </div>
              </div>
              <button
                onClick={onLogout}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
          <nav className="p-4">
            <div className="space-y-2">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-600 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default EmployeeDashboard;