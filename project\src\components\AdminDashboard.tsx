import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Clock, 
  FileText, 
  Settings, 
  Plus, 
  Search,
  Filter,
  Bell,
  LogOut,
  Building2,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Trash2,
  X,
  Home,
  UserCheck,
  ClipboardList,
  CreditCard,
  BookOpen,
  Phone,
  Briefcase,
  TrendingUp,
  BarChart3,
  Crown,
  UserCog
} from 'lucide-react';
import { User, Employee, LeaveRequest, PendingApproval } from '../App';

interface AdminDashboardProps {
  user: User;
  onLogout: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddEmployeeModal, setShowAddEmployeeModal] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState('05/06/2025 - 05/07/2025');
  const [newEmployee, setNewEmployee] = useState({
    name: '',
    email: '',
    phone: '',
    department: '',
    role: 'employee' as 'employee' | 'supervisor' | 'secretary',
    jobTitle: '',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    supervisorId: '',
    address: '',
    dateOfBirth: '',
    emergencyContact: '',
    emergencyPhone: '',
    salary: '',
    bloodGroup: '',
    nationality: '',
    maritalStatus: '',
    qualification: '',
    experience: '',
    workLocation: '',
    employmentType: ''
  });

  // Firebase is now used instead of AppState

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Import Firebase services
        const { getEmployees, getLeaveRequests, getPendingApprovals } = await import('../services/firebaseService');
        
        // Fetch data from Firestore
        const employeesData = await getEmployees();
        const leaveRequestsData = await getLeaveRequests();
        const pendingApprovalsData = await getPendingApprovals();
        
        // Update state
        setEmployees(employeesData);
        setLeaveRequests(leaveRequestsData);
        setPendingApprovals(pendingApprovalsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        alert('Failed to load data. Please refresh the page.');
      }
    };
    
    fetchData();
  }, []);

  // Get supervisors for assignment dropdown
  const [supervisors, setSupervisors] = useState<Employee[]>([]);
  
  // Fetch supervisors
  useEffect(() => {
    const fetchSupervisors = async () => {
      try {
        // Import Firebase service
        const { getSupervisors } = await import('../services/firebaseService');
        
        // Fetch supervisors from Firestore
        const supervisorsData = await getSupervisors();
        setSupervisors(supervisorsData);
      } catch (error) {
        console.error('Error fetching supervisors:', error);
      }
    };
    
    fetchSupervisors();
  }, []);

  const stats = [
    { 
      label: 'Total Employees', 
      value: employees.filter(emp => emp.role === 'employee').length.toString(), 
      icon: <Users className="w-6 h-6" />,
      color: 'bg-emerald-100 text-emerald-600',
      bgColor: 'bg-emerald-50'
    },
    { 
      label: 'Supervisors', 
      value: employees.filter(emp => emp.role === 'supervisor').length.toString(), 
      icon: <Crown className="w-6 h-6" />,
      color: 'bg-purple-100 text-purple-600',
      bgColor: 'bg-purple-50'
    },
    { 
      label: 'Secretaries', 
      value: employees.filter(emp => emp.role === 'secretary').length.toString(), 
      icon: <UserCog className="w-6 h-6" />,
      color: 'bg-blue-100 text-blue-600',
      bgColor: 'bg-blue-50'
    },
    { 
      label: 'Total Absent', 
      value: employees.filter(emp => emp.status === 'Absent').length.toString(), 
      icon: <Calendar className="w-6 h-6" />,
      color: 'bg-red-100 text-red-600',
      bgColor: 'bg-red-50'
    }
  ];

  const chartData = [
    { date: '2025-06-29', present: 85, absent: 10, halfDay: 5 },
    { date: '2025-06-30', present: 90, absent: 8, halfDay: 2 },
    { date: '2025-07-01', present: 88, absent: 7, halfDay: 5 },
    { date: '2025-07-02', present: 92, absent: 5, halfDay: 3 },
    { date: '2025-07-03', present: 87, absent: 9, halfDay: 4 },
    { date: '2025-07-04', present: 95, absent: 3, halfDay: 2 },
    { date: '2025-07-05', present: 93, absent: 4, halfDay: 3 }
  ];

  const handleAddEmployee = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newEmployee.name && newEmployee.email && newEmployee.phone && newEmployee.department && newEmployee.jobTitle) {
      try {
        // Import the Firebase service
        const { addEmployee } = await import('../services/firebaseService');
        
        // Create employee with Firebase
        const employeeData = {
          ...newEmployee,
          employeeId: `${newEmployee.role.toUpperCase().slice(0,3)}${(employees.length + 1).toString().padStart(3, '0')}`,
          status: 'Absent',
          joinDate: new Date().toISOString().split('T')[0]
        };
        
        // Add employee to Firestore
        await addEmployee(employeeData);
        
        // Reset form
        setNewEmployee({
          name: '',
          email: '',
          phone: '',
          department: '',
          role: 'employee',
          jobTitle: '',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          supervisorId: '',
          address: '',
          dateOfBirth: '',
          emergencyContact: '',
          emergencyPhone: '',
          salary: '',
          bloodGroup: '',
          nationality: '',
          maritalStatus: '',
          qualification: '',
          experience: '',
          workLocation: '',
          employmentType: ''
        });
        setShowAddEmployeeModal(false);
        
        // Refresh employee list
        const { getEmployees } = await import('../services/firebaseService');
        const updatedEmployees = await getEmployees();
        setEmployees(updatedEmployees);
      } catch (error) {
        console.error('Error adding employee:', error);
        alert('Failed to add employee. Please try again.');
      }
    }
  };

  const handleDeleteEmployee = async (id: string) => {
    if (confirm('Are you sure you want to delete this employee?')) {
      try {
        // Import the Firebase service
        const { deleteEmployee } = await import('../services/firebaseService');
        
        // Delete employee from Firestore
        await deleteEmployee(id);
        
        // Refresh employee list
        const { getEmployees } = await import('../services/firebaseService');
        const updatedEmployees = await getEmployees();
        setEmployees(updatedEmployees);
      } catch (error) {
        console.error('Error deleting employee:', error);
        alert('Failed to delete employee. Please try again.');
      }
    }
  };

  const handleApproval = async (id: string, action: 'approve' | 'reject') => {
    try {
      // Import Firebase services
      const { approveRequest, rejectRequest, getLeaveRequests, getPendingApprovals } = await import('../services/firebaseService');
      
      if (action === 'approve') {
        // Approve request in Firestore
        await approveRequest(id);
        
        // Refresh data
        const updatedLeaveRequests = await getLeaveRequests();
        setLeaveRequests(updatedLeaveRequests);
      } else {
        // Reject request in Firestore
        await rejectRequest(id);
      }
      
      // Refresh pending approvals in both cases
      const updatedPendingApprovals = await getPendingApprovals();
      setPendingApprovals(updatedPendingApprovals);
    } catch (error) {
      console.error(`Error ${action}ing request:`, error);
      alert(`Failed to ${action} request. Please try again.`);
    }
  };

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.jobTitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const menuItems = [
    { id: 'overview', label: 'Home', icon: <Home className="w-5 h-5" /> },
    { id: 'employees', label: 'Employees', icon: <Users className="w-5 h-5" /> },
    { id: 'attendance', label: 'Attendance & Payroll', icon: <Clock className="w-5 h-5" />, hasSubmenu: true },
    { id: 'approvals', label: 'Employee Approvals', icon: <UserCheck className="w-5 h-5" />, hasSubmenu: true },
    { id: 'documents', label: 'Employee Documents', icon: <ClipboardList className="w-5 h-5" /> },
    { id: 'overtime', label: 'Overtime', icon: <Clock className="w-5 h-5" /> },
    { id: 'remarks', label: 'Remarks', icon: <FileText className="w-5 h-5" /> },
    { id: 'cashbook', label: 'Cashbook', icon: <CreditCard className="w-5 h-5" /> },
    { id: 'business-contact', label: 'Business Contact', icon: <Phone className="w-5 h-5" /> },
    { id: 'business-holidays', label: 'Business Holidays', icon: <Calendar className="w-5 h-5" /> },
    { id: 'inventory', label: 'Inventory Management', icon: <Briefcase className="w-5 h-5" />, hasSubmenu: true },
    { id: 'payslip', label: 'Payslip Management', icon: <DollarSign className="w-5 h-5" />, hasSubmenu: true },
    { id: 'vehicle', label: 'Vehicle Management', icon: <Briefcase className="w-5 h-5" />, hasSubmenu: true }
  ];

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'supervisor':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-600">
          <Crown className="w-3 h-3 mr-1" />
          Supervisor
        </span>;
      case 'secretary':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
          <UserCog className="w-3 h-3 mr-1" />
          Secretary
        </span>;
      default:
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
          Employee
        </span>;
    }
  };

  const getSupervisorName = (supervisorId?: string) => {
    if (!supervisorId) return 'Unassigned';
    const supervisor = employees.find(emp => emp.id === supervisorId && emp.role === 'supervisor');
    return supervisor ? supervisor.name : 'Unassigned';
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Header with Date Range */}
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">Home</h1>
              <div className="flex items-center space-x-4">
                <input
                  type="text"
                  value={selectedDateRange}
                  onChange={(e) => setSelectedDateRange(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="p-2 text-gray-600 hover:text-gray-900">
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className={`${stat.bgColor} rounded-xl p-6 border border-gray-200 hover:shadow-md transition-shadow`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${stat.color}`}>
                      {stat.icon}
                    </div>
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</h3>
                  <p className="text-gray-600 text-sm">{stat.label}</p>
                </div>
              ))}
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Employee Attendance Insights */}
              <div className="lg:col-span-2 bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Employee Attendance Insights</h3>
                  <div className="flex items-center space-x-4">
                    <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                      <option>Weekly</option>
                      <option>Monthly</option>
                      <option>Yearly</option>
                    </select>
                    <button className="text-sm text-gray-600 hover:text-gray-900">
                      Show Not Set Values
                    </button>
                  </div>
                </div>

                {/* Enhanced Chart Area */}
                <div className="h-80 relative mb-4">
                  {/* Y-axis labels */}
                  <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pr-4">
                    <span>1</span>
                    <span>0.8</span>
                    <span>0.6</span>
                    <span>0.4</span>
                    <span>0.2</span>
                    <span>0</span>
                  </div>

                  {/* Chart container */}
                  <div className="ml-8 h-full relative">
                    {/* Grid lines */}
                    <div className="absolute inset-0">
                      {[0, 20, 40, 60, 80, 100].map((percent) => (
                        <div
                          key={percent}
                          className="absolute w-full border-t border-gray-100"
                          style={{ bottom: `${percent}%` }}
                        />
                      ))}
                    </div>

                    {/* Chart SVG */}
                    <svg className="w-full h-full" viewBox="0 0 700 300">
                      {/* Present line (green) */}
                      <polyline
                        fill="none"
                        stroke="#10b981"
                        strokeWidth="3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        points="50,50 150,40 250,45 350,35 450,50 550,25 650,30"
                      />
                      
                      {/* Absent line (red) */}
                      <polyline
                        fill="none"
                        stroke="#ef4444"
                        strokeWidth="3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        points="50,200 150,180 250,190 350,170 450,185 550,160 650,165"
                      />
                      
                      {/* Half Day line (yellow) */}
                      <polyline
                        fill="none"
                        stroke="#f59e0b"
                        strokeWidth="3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        points="50,250 150,240 250,245 350,235 450,240 550,230 650,235"
                      />

                      {/* Data points for Present */}
                      {chartData.map((data, index) => (
                        <g key={`present-${index}`}>
                          <circle 
                            cx={50 + index * 100} 
                            cy={300 - (data.present * 2.5)} 
                            r="5" 
                            fill="#10b981"
                            className="hover:r-7 transition-all cursor-pointer"
                          />
                        </g>
                      ))}

                      {/* Data points for Absent */}
                      {chartData.map((data, index) => (
                        <g key={`absent-${index}`}>
                          <circle 
                            cx={50 + index * 100} 
                            cy={300 - (data.absent * 10)} 
                            r="5" 
                            fill="#ef4444"
                            className="hover:r-7 transition-all cursor-pointer"
                          />
                        </g>
                      ))}

                      {/* Data points for Half Day */}
                      {chartData.map((data, index) => (
                        <g key={`halfday-${index}`}>
                          <circle 
                            cx={50 + index * 100} 
                            cy={300 - (data.halfDay * 20)} 
                            r="5" 
                            fill="#f59e0b"
                            className="hover:r-7 transition-all cursor-pointer"
                          />
                        </g>
                      ))}
                    </svg>
                  </div>
                </div>

                {/* Legend */}
                <div className="flex items-center justify-center space-x-8 text-sm mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-emerald-500 rounded-full"></div>
                    <span className="text-gray-600">Presents</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                    <span className="text-gray-600">Absent</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                    <span className="text-gray-600">Half Day</span>
                  </div>
                </div>

                {/* Date labels */}
                <div className="ml-8 flex justify-between text-xs text-gray-500">
                  {chartData.map((data) => (
                    <span key={data.date}>{data.date.split('-')[2]}/{data.date.split('-')[1]}</span>
                  ))}
                </div>
              </div>

              {/* Holiday Calendar */}
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Holiday Calendar</h3>
                
                <div className="text-center mb-6">
                  <div className="inline-flex items-center justify-center w-24 h-24 bg-blue-100 rounded-full mb-4">
                    <div className="text-center">
                      <div className="text-xs text-blue-600 font-medium">JUN</div>
                      <div className="text-2xl font-bold text-blue-600">17</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-center">
                    <img 
                      src="https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face" 
                      alt="Employee" 
                      className="w-16 h-16 rounded-full"
                    />
                    <div className="ml-2 text-yellow-500">
                      <AlertCircle className="w-5 h-5" />
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-lg font-semibold text-gray-900 mb-2">No Holiday Added</p>
                  <p className="text-sm text-gray-600 mb-4">Add new holidays through Holiday tab.</p>
                </div>
              </div>
            </div>

            {/* Recent Activities */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Pending Approvals</h3>
                <span className="bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full text-sm font-medium">
                  {pendingApprovals.length} pending
                </span>
              </div>
              <div className="space-y-4">
                {pendingApprovals.slice(0, 5).map((approval) => (
                  <div key={approval.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="flex items-center space-x-3">
                      <img
                        src={approval.avatar}
                        alt={approval.employeeName}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <p className="font-medium text-gray-900">{approval.employeeName}</p>
                        <p className="text-sm text-gray-600">
                          {approval.type === 'check-in' ? 'Check-in' :
                           approval.type === 'check-out' ? 'Check-out' :
                           'Leave request'} at {approval.time}
                        </p>
                        <p className="text-xs text-gray-500">{approval.reason}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleApproval(approval.id, 'approve')}
                        className="p-2 text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors"
                        title="Approve"
                      >
                        <CheckCircle className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleApproval(approval.id, 'reject')}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Reject"
                      >
                        <XCircle className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                ))}
                {pendingApprovals.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>No pending approvals</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'employees':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Employee Management</h2>
              <button 
                onClick={() => setShowAddEmployeeModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add Employee</span>
              </button>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex space-x-4">
                  <div className="flex-1 relative">
                    <Search className="w-5 h-5 absolute left-3 top-3 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <Filter className="w-4 h-4" />
                    <span>Filter</span>
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-4 font-medium text-gray-900">Employee</th>
                      <th className="text-left p-4 font-medium text-gray-900">Role</th>
                      <th className="text-left p-4 font-medium text-gray-900">Job Title</th>
                      <th className="text-left p-4 font-medium text-gray-900">Department</th>
                      <th className="text-left p-4 font-medium text-gray-900">Supervisor</th>
                      <th className="text-left p-4 font-medium text-gray-900">Status</th>
                      <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredEmployees.map((employee) => (
                      <tr key={employee.id} className="hover:bg-gray-50 transition-colors">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <img
                              src={employee.avatar}
                              alt={employee.name}
                              className="w-10 h-10 rounded-full"
                            />
                            <div>
                              <span className="font-medium text-gray-900">{employee.name}</span>
                              <p className="text-sm text-gray-500">{employee.employeeId}</p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">{getRoleBadge(employee.role)}</td>
                        <td className="p-4 text-gray-600">{employee.jobTitle}</td>
                        <td className="p-4 text-gray-600">{employee.department}</td>
                        <td className="p-4 text-gray-600">
                          {employee.role === 'supervisor' ? 'N/A' : getSupervisorName(employee.supervisorId)}
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            employee.status === 'Present' ? 'bg-emerald-100 text-emerald-600' :
                            employee.status === 'On Leave' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {employee.status}
                          </span>
                        </td>
                        <td className="p-4">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-700 p-1 rounded transition-colors">
                              <Edit className="w-4 h-4" />
                            </button>
                            <button 
                              onClick={() => handleDeleteEmployee(employee.id)}
                              className="text-red-600 hover:text-red-700 p-1 rounded transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Add Employee Modal */}
            {showAddEmployeeModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Add New Employee</h3>
                    <button
                      onClick={() => setShowAddEmployeeModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                  <form onSubmit={handleAddEmployee} className="space-y-6">
                    {/* Basic Information */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                          <input
                            type="text"
                            value={newEmployee.name}
                            onChange={(e) => setNewEmployee({...newEmployee, name: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                          <input
                            type="email"
                            value={newEmployee.email}
                            onChange={(e) => setNewEmployee({...newEmployee, email: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Phone *</label>
                          <input
                            type="tel"
                            value={newEmployee.phone}
                            onChange={(e) => setNewEmployee({...newEmployee, phone: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                          <input
                            type="date"
                            value={newEmployee.dateOfBirth}
                            onChange={(e) => setNewEmployee({...newEmployee, dateOfBirth: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                          <textarea
                            value={newEmployee.address}
                            onChange={(e) => setNewEmployee({...newEmployee, address: e.target.value})}
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Work Information */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Work Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Role *</label>
                          <select
                            value={newEmployee.role}
                            onChange={(e) => setNewEmployee({...newEmployee, role: e.target.value as 'employee' | 'supervisor' | 'secretary'})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          >
                            <option value="employee">Employee</option>
                            <option value="supervisor">Supervisor</option>
                            <option value="secretary">Secretary</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Job Title *</label>
                          <input
                            type="text"
                            value={newEmployee.jobTitle}
                            onChange={(e) => setNewEmployee({...newEmployee, jobTitle: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="e.g., Senior Developer, Manager"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Department *</label>
                          <select
                            value={newEmployee.department}
                            onChange={(e) => setNewEmployee({...newEmployee, department: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          >
                            <option value="">Select Department</option>
                            <option value="Engineering">Engineering</option>
                            <option value="Design">Design</option>
                            <option value="HR">HR</option>
                            <option value="Finance">Finance</option>
                            <option value="Marketing">Marketing</option>
                            <option value="Sales">Sales</option>
                            <option value="Administration">Administration</option>
                          </select>
                        </div>
                        {(newEmployee.role === 'employee' || newEmployee.role === 'secretary') && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Assign to Supervisor</label>
                            <select
                              value={newEmployee.supervisorId}
                              onChange={(e) => setNewEmployee({...newEmployee, supervisorId: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                              <option value="">Select Supervisor</option>
                              {supervisors.map(supervisor => (
                                <option key={supervisor.id} value={supervisor.id}>
                                  {supervisor.name} - {supervisor.department}
                                </option>
                              ))}
                            </select>
                          </div>
                        )}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Work Location</label>
                          <input
                            type="text"
                            value={newEmployee.workLocation}
                            onChange={(e) => setNewEmployee({...newEmployee, workLocation: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="e.g., Office, Remote, Hybrid"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Employment Type</label>
                          <select
                            value={newEmployee.employmentType}
                            onChange={(e) => setNewEmployee({...newEmployee, employmentType: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="">Select Type</option>
                            <option value="Full-time">Full-time</option>
                            <option value="Part-time">Part-time</option>
                            <option value="Contract">Contract</option>
                            <option value="Intern">Intern</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Additional Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact</label>
                          <input
                            type="text"
                            value={newEmployee.emergencyContact}
                            onChange={(e) => setNewEmployee({...newEmployee, emergencyContact: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Phone</label>
                          <input
                            type="tel"
                            value={newEmployee.emergencyPhone}
                            onChange={(e) => setNewEmployee({...newEmployee, emergencyPhone: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Salary</label>
                          <input
                            type="number"
                            value={newEmployee.salary}
                            onChange={(e) => setNewEmployee({...newEmployee, salary: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Annual salary"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Blood Group</label>
                          <select
                            value={newEmployee.bloodGroup}
                            onChange={(e) => setNewEmployee({...newEmployee, bloodGroup: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="">Select Blood Group</option>
                            <option value="A+">A+</option>
                            <option value="A-">A-</option>
                            <option value="B+">B+</option>
                            <option value="B-">B-</option>
                            <option value="AB+">AB+</option>
                            <option value="AB-">AB-</option>
                            <option value="O+">O+</option>
                            <option value="O-">O-</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Nationality</label>
                          <input
                            type="text"
                            value={newEmployee.nationality}
                            onChange={(e) => setNewEmployee({...newEmployee, nationality: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
                          <select
                            value={newEmployee.maritalStatus}
                            onChange={(e) => setNewEmployee({...newEmployee, maritalStatus: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="">Select Status</option>
                            <option value="Single">Single</option>
                            <option value="Married">Married</option>
                            <option value="Divorced">Divorced</option>
                            <option value="Widowed">Widowed</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Qualification</label>
                          <input
                            type="text"
                            value={newEmployee.qualification}
                            onChange={(e) => setNewEmployee({...newEmployee, qualification: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="e.g., Bachelor's in Computer Science"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Experience</label>
                          <input
                            type="text"
                            value={newEmployee.experience}
                            onChange={(e) => setNewEmployee({...newEmployee, experience: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="e.g., 5 years"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setShowAddEmployeeModal(false)}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Add Employee
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
            <p className="text-gray-600">This feature is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Building2 className="w-8 h-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">Praskla Technology</span>
              </div>
              <div className="bg-gradient-to-r from-pink-500 to-orange-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                ⭐ Upgrade to Premium
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="w-5 h-5" />
                {pendingApprovals.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {pendingApprovals.length}
                  </span>
                )}
              </button>
              <div className="flex items-center space-x-3 bg-gray-100 rounded-full pr-3 py-1">
                <img src={user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'} alt="User Avatar" className="w-9 h-9 rounded-full object-cover" />
                <span className="font-medium text-gray-800">{user.name}</span>
              </div>
              <button
                onClick={onLogout}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
          <nav className="p-4">
            <div className="space-y-1">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center justify-between px-4 py-3 rounded-lg transition-colors text-left ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-600 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {item.icon}
                    <span className="text-sm">{item.label}</span>
                  </div>
                  {item.hasSubmenu && (
                    <div className="text-gray-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;