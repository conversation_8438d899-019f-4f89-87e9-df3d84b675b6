import React, { useState } from 'react';
import { initializeFirebaseData } from '../scripts/initFirebaseData';

const FirebaseInitializer: React.FC = () => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInitialize = async () => {
    if (isInitializing) return;
    
    setIsInitializing(true);
    setError(null);
    
    try {
      await initializeFirebaseData();
      setIsComplete(true);
    } catch (err) {
      console.error('Initialization error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg z-50 max-w-md">
      <h3 className="text-lg font-semibold mb-2">Firebase Database Initializer</h3>
      <p className="text-sm text-gray-600 mb-4">
        This will create sample users and data in your Firebase database.
        Use this only for development and testing purposes.
      </p>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p className="text-sm">{error}</p>
        </div>
      )}
      
      {isComplete && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p className="text-sm">Firebase database initialized successfully!</p>
        </div>
      )}
      
      <button
        onClick={handleInitialize}
        disabled={isInitializing}
        className={`w-full py-2 px-4 rounded-md ${isInitializing
          ? 'bg-gray-300 cursor-not-allowed'
          : 'bg-blue-600 text-white hover:bg-blue-700'}`}
      >
        {isInitializing ? 'Initializing...' : 'Initialize Firebase Database'}
      </button>
    </div>
  );
};

export default FirebaseInitializer;